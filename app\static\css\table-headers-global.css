/* 全局表头样式优化 - 使用系统主题色 */

/* 所有表格表头统一使用主题色 */
.table thead th,
.table-sm thead th,
.table-bordered thead th,
.table-striped thead th,
.table-hover thead th,
.thead-dark th,
.thead-light th,
table thead th,
table th {
    font-weight: 500 !important;
    font-size: 13px !important;
    color: white !important;
    background-color: var(--theme-primary) !important;
    border-bottom: 2px solid var(--theme-primary-dark) !important;
    border-right: 1px solid var(--theme-primary-dark) !important;
    padding: 0.75rem 0.5rem !important;
    vertical-align: middle !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    text-align: center !important;
    white-space: nowrap !important;
}

/* 最后一列表头不需要右边框 */
.table thead th:last-child,
.table-sm thead th:last-child,
.table-bordered thead th:last-child,
.table-striped thead th:last-child,
.table-hover thead th:last-child,
table thead th:last-child,
table th:last-child {
    border-right: none !important;
}

/* 表头悬停效果 */
.table thead th:hover {
    background-color: var(--theme-primary-dark) !important;
    cursor: default;
}

/* 深色表头样式 - 统一使用主题色 */
.thead-dark th {
    background-color: var(--theme-primary-dark) !important;
    color: white !important;
    border-color: var(--theme-primary) !important;
}

/* 浅色表头样式 - 统一使用主题色 */
.thead-light th {
    background-color: var(--theme-primary-light) !important;
    color: white !important;
    border-color: var(--theme-primary) !important;
}

/* 表格行样式优化 */
.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}

/* 紧凑表格样式 */
.table-sm th,
.table-sm td {
    padding: 0.5rem 0.25rem;
}

/* 响应式表格优化 */
@media (max-width: 768px) {
    .table thead th,
    .table-sm thead th {
        font-size: 0.8rem !important;
        padding: 0.5rem 0.25rem !important;
    }
}

/* 特定页面的表格优化 */
.financial-vouchers-table th {
    font-weight: normal !important;
    font-size: 0.875rem !important;
    color: #6c757d !important;
}

.financial-vouchers-table .voucher-number-col {
    width: 150px !important;
    min-width: 150px !important;
    max-width: 150px !important;
}

/* 凭证号列样式 */
.voucher-number {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    font-weight: 600;
    word-break: break-all;
    white-space: normal;
    line-height: 1.2;
}

/* 金额列样式 */
.amount-column {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    text-align: right;
}

/* 状态徽章样式 */
.status-badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
}

/* 操作按钮样式 */
.action-buttons .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* 隐藏创建人列 */
.hide-creator-column {
    display: none !important;
}

/* 表格边框优化 */
.table-bordered {
    border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

/* 表格条纹样式 */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 表格悬停效果 */
.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}
